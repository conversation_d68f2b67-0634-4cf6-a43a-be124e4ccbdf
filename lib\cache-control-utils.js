/**
 * Cache Control Utilities for Ocean Soul Sparkles
 *
 * This module provides utilities for managing cache-control headers
 * across different types of API requests and data freshness requirements.
 */

/**
 * Cache strategies for different data types
 */
export const CACHE_STRATEGIES = {
  // Real-time data that changes frequently
  REAL_TIME: 'no-cache, no-store, must-revalidate',

  // Semi-static data that can be cached for a few minutes
  SEMI_STATIC: 'max-age=300, stale-while-revalidate=60', // 5 minutes cache, 1 minute stale

  // Admin data with shorter cache
  ADMIN: 'max-age=60, no-cache', // 1 minute cache for admin data

  // Static data that rarely changes
  STATIC: 'max-age=3600, stale-while-revalidate=300', // 1 hour cache, 5 minutes stale

  // Public data with moderate cache
  PUBLIC: 'max-age=180, stale-while-revalidate=30', // 3 minutes cache, 30 seconds stale

  // No cache for mutations
  NO_CACHE: 'no-cache, no-store, must-revalidate'
};

/**
 * Determine cache strategy based on endpoint and request characteristics
 * @param {string} endpoint - The API endpoint or table name
 * @param {string} method - HTTP method (GET, POST, etc.)
 * @param {boolean} isAdmin - Whether this is an admin request
 * @param {Object} queryParams - Query parameters object
 * @returns {string} Cache-control header value
 */
export function getCacheStrategy(endpoint, method = 'GET', isAdmin = false, queryParams = {}) {
  // No cache for non-GET requests
  if (method.toUpperCase() !== 'GET') {
    return CACHE_STRATEGIES.NO_CACHE;
  }

  // Admin requests get shorter cache
  if (isAdmin) {
    return CACHE_STRATEGIES.ADMIN;
  }

  // Real-time data endpoints
  if (isRealTimeEndpoint(endpoint, queryParams)) {
    return CACHE_STRATEGIES.REAL_TIME;
  }

  // Semi-static data endpoints
  if (isSemiStaticEndpoint(endpoint)) {
    return CACHE_STRATEGIES.SEMI_STATIC;
  }

  // Static data endpoints
  if (isStaticEndpoint(endpoint)) {
    return CACHE_STRATEGIES.STATIC;
  }

  // Default to public cache strategy
  return CACHE_STRATEGIES.PUBLIC;
}

/**
 * Check if endpoint contains real-time data that should not be cached
 * @param {string} endpoint - The endpoint or URL
 * @param {Object} queryParams - Query parameters
 * @returns {boolean} True if this is real-time data
 */
function isRealTimeEndpoint(endpoint, queryParams = {}) {
  const endpointLower = endpoint.toLowerCase();

  // Booking-related endpoints
  if (endpointLower.includes('bookings')) {
    // Pending bookings are real-time
    if (queryParams.status === 'pending' || endpointLower.includes('status=eq.pending')) {
      return true;
    }
    // Recent bookings (within last day) are real-time
    if (queryParams.recent === 'true' || endpointLower.includes('recent=true')) {
      return true;
    }
  }

  // Payment-related endpoints
  if (endpointLower.includes('payments')) {
    // Completed payments for revenue tracking
    if (queryParams.payment_status === 'completed' || endpointLower.includes('payment_status=eq.completed')) {
      return true;
    }
    // Payment stats and summaries
    if (endpointLower.includes('stats') || endpointLower.includes('summary')) {
      return true;
    }
  }

  // Real-time analytics
  if (endpointLower.includes('analytics') && endpointLower.includes('real-time')) {
    return true;
  }

  return false;
}

/**
 * Check if endpoint contains semi-static data
 * @param {string} endpoint - The endpoint or URL
 * @returns {boolean} True if this is semi-static data
 */
function isSemiStaticEndpoint(endpoint) {
  const endpointLower = endpoint.toLowerCase();

  return (
    endpointLower.includes('customers') ||
    endpointLower.includes('products') ||
    endpointLower.includes('inventory') ||
    (endpointLower.includes('services') && !endpointLower.includes('services_with_pricing'))
  );
}

/**
 * Check if endpoint contains static data
 * @param {string} endpoint - The endpoint or URL
 * @returns {boolean} True if this is static data
 */
function isStaticEndpoint(endpoint) {
  const endpointLower = endpoint.toLowerCase();

  return (
    endpointLower.includes('services_with_pricing') ||
    endpointLower.includes('settings') ||
    endpointLower.includes('config') ||
    endpointLower.includes('metadata')
  );
}

/**
 * Add cache-control headers to a fetch options object
 * @param {Object} options - Fetch options object
 * @param {string} endpoint - The endpoint being called
 * @param {string} method - HTTP method
 * @param {boolean} isAdmin - Whether this is an admin request
 * @param {Object} queryParams - Query parameters
 * @returns {Object} Enhanced options with cache headers
 */
export function addCacheHeaders(options = {}, endpoint = '', method = 'GET', isAdmin = false, queryParams = {}) {
  const cacheStrategy = getCacheStrategy(endpoint, method, isAdmin, queryParams);

  return {
    ...options,
    headers: {
      ...options.headers,
      'Cache-Control': cacheStrategy,
      ...(cacheStrategy.includes('no-cache') && { 'Pragma': 'no-cache' })
    }
  };
}

/**
 * Set cache-control headers on a response object (for API routes)
 * @param {Object} res - Express/Next.js response object
 * @param {string} endpoint - The endpoint being served
 * @param {string} method - HTTP method
 * @param {boolean} isAdmin - Whether this is an admin request
 * @param {Object} queryParams - Query parameters
 */
export function setCacheHeaders(res, endpoint = '', method = 'GET', isAdmin = false, queryParams = {}) {
  const cacheStrategy = getCacheStrategy(endpoint, method, isAdmin, queryParams);

  res.setHeader('Cache-Control', cacheStrategy);

  if (cacheStrategy.includes('no-cache')) {
    res.setHeader('Pragma', 'no-cache');
    res.setHeader('Expires', '0');
  }
}

/**
 * Parse query string to extract parameters for cache strategy determination
 * @param {string} queryString - URL query string
 * @returns {Object} Parsed query parameters
 */
export function parseQueryParams(queryString) {
  if (!queryString) return {};

  const params = {};
  const searchParams = new URLSearchParams(queryString);

  for (const [key, value] of searchParams.entries()) {
    params[key] = value;
  }

  return params;
}

/**
 * Extract endpoint name from a full URL
 * @param {string} url - Full URL or endpoint path
 * @returns {string} Extracted endpoint name
 */
export function extractEndpoint(url) {
  if (!url) return '';

  try {
    const urlObj = new URL(url);
    return urlObj.pathname;
  } catch {
    // If URL parsing fails, assume it's already a path
    return url;
  }
}

/**
 * Get cache headers for client-side fetch requests
 * @param {string} endpoint - The endpoint being called
 * @param {string} method - HTTP method
 * @param {boolean} isAdmin - Whether this is an admin request
 * @param {Object} queryParams - Query parameters
 * @returns {Object} Headers object with cache-control
 */
export function getClientCacheHeaders(endpoint = '', method = 'GET', isAdmin = false, queryParams = {}) {
  const cacheStrategy = getCacheStrategy(endpoint, method, isAdmin, queryParams);

  const headers = {
    'Cache-Control': cacheStrategy
  };

  if (cacheStrategy.includes('no-cache')) {
    headers['Pragma'] = 'no-cache';
  }

  return headers;
}

/**
 * Enhanced fetch function with automatic cache headers
 * @param {string} url - URL to fetch
 * @param {Object} options - Fetch options
 * @param {boolean} isAdmin - Whether this is an admin request
 * @returns {Promise<Response>} Fetch response
 */
export async function fetchWithCache(url, options = {}, isAdmin = false) {
  const endpoint = extractEndpoint(url);
  const method = options.method || 'GET';
  const queryParams = parseQueryParams(url.split('?')[1]);

  const cacheHeaders = getClientCacheHeaders(endpoint, method, isAdmin, queryParams);

  return fetch(url, {
    ...options,
    headers: {
      ...options.headers,
      ...cacheHeaders
    }
  });
}
